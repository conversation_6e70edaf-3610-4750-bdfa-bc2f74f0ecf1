import React, { useState, useMemo, useCallback, useEffect, lazy, Suspense } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { jobsData } from '../data/jobsData';
import Header from './Header';
import Footer from './Footer';
import '../job-detail.css';
import './ProjectImageSwiper.css';

// Lazy load heavy components
const ProjectImageSwiper = lazy(() => import('./ProjectImageSwiper'));
const NDANotification = lazy(() => import('./NDANotification'));

const JobDetail = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const job = jobsData.find(job => job.slug === slug);
  const [ndaNotification, setNdaNotification] = useState({ isOpen: false, projectTitle: '' });
  const [isScrolled, setIsScrolled] = useState(false);

  // Scroll to top when component mounts or slug changes
  useEffect(() => {
    // Scroll to the very top of the page
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });

    // Alternative method for better browser compatibility
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
  }, [slug]); // Re-run when slug changes (different job selected)

  // Handle scroll events for back button transparency
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      setIsScrolled(scrollTop > 100); // Make transparent after scrolling 100px
    };

    window.addEventListener('scroll', handleScroll);

    // Check initial scroll position
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Smart back navigation handler
  const handleBackNavigation = useCallback((e) => {
    e.preventDefault();

    // Get the stored scroll position for this job
    const storedPosition = sessionStorage.getItem(`timeline-scroll-${slug}`);

    // Navigate back to home with experience section
    navigate('/', { replace: true });

    // Wait for navigation to complete, then scroll to the stored position
    setTimeout(() => {
      if (storedPosition) {
        const position = parseInt(storedPosition, 10);
        window.scrollTo({
          top: position,
          behavior: 'smooth'
        });
      } else {
        // Fallback: scroll to experience section
        const experienceSection = document.querySelector('.experience');
        if (experienceSection) {
          experienceSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    }, 100);
  }, [slug, navigate]);

  // Memoized NDA check
  const isNDAProject = useCallback((project) => {
    return project.description.toLowerCase().includes('nda') ||
           project.title.toLowerCase().includes('nda') ||
           project.images.some(img => img.includes('NDA'));
  }, []);

  // Memoized handler
  const handleProjectInfoClick = useCallback((e, project) => {
    e.stopPropagation();
    if (isNDAProject(project)) {
      setNdaNotification({ isOpen: true, projectTitle: project.title });
      return;
    }
    window.open(project.liveUrl, '_blank');
  }, [isNDAProject]);

  const closeNdaNotification = useCallback(() => {
    setNdaNotification({ isOpen: false, projectTitle: '' });
  }, []);

  if (!job) {
    return (
      <div>
        <Header />
        <div style={{ padding: '100px 20px', textAlign: 'center', color: 'white' }}>
          <h1>Job Not Found</h1>
          <Link to="/" style={{ color: '#4B0082' }}>← Back to Home</Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div>
      <Header />
      
      {/* Navigation Back */}
      <div className="back-navigation">
        <button
          onClick={handleBackNavigation}
          className={`back-button ${isScrolled ? 'scrolled' : ''}`}
        >
          <span className="back-arrow">←</span>
          <span className="back-text-desktop">Back to Timeline</span>
          <span className="back-text-mobile">
            <span className="swipe-indicator">←</span>
            Swipe to back
          </span>
        </button>
      </div>

      {/* Job Detail Hero Section */}
      <section className="job-hero">
        <div className="job-hero-content">
          <div className="company-branding">
            <img 
              src={job.logo} 
              alt={job.logoAlt} 
              className="hero-company-logo" 
              loading="lazy"
            />
            <div className="company-info">
              <h1 className="job-title-hero">{job.title}</h1>
              <h2 className="company-name-hero">{job.company}</h2>
              {job.companyLink && (
                <p className="company-link-hero">
                  <a href={job.companyLink} target="_blank" rel="noopener noreferrer">
                    {job.companyLink}
                  </a>
                </p>
              )}
              <p className="job-duration-hero">{job.duration}</p>
            </div>
          </div>
          <div className="job-summary">
            <p>{job.summary}</p>
          </div>
        </div>
      </section>

      {/* Job Details Content */}
      <section className="job-content">
        <div className="content-grid">
          {/* Full Job Description */}
          <div className="content-card">
            <h3>Role Overview</h3>
            <p>{job.roleOverview}</p>
            
            <h4>Key Responsibilities</h4>
            <ul>
              {job.responsibilities.map((responsibility, index) => (
                <li key={index}>{responsibility}</li>
              ))}
            </ul>
          </div>

          {/* Skills & Technologies */}
          <div className="content-card">
            <h3>Technologies & Skills</h3>
            <div className="skills-grid">
              {Object.entries(job.skills).map(([category, skills]) => {
                // Generate class name based on category name
                const categoryClass = category.toLowerCase().replace(/[^a-z0-9]/g, '_') + '_skills';
                return (
                  <div key={category} className={`skill-category ${categoryClass}`}>
                    <h4>{category}</h4>
                    <div className="skill-tags">
                      {skills.map((skill, index) => (
                        <span key={index} className="skill-tag">{skill}</span>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Key Accomplishments */}
          <div className="content-card">
            <h3>Key Accomplishments</h3>
            <div className="accomplishments-list">
              {job.accomplishments.map((accomplishment, index) => (
                <div key={index} className="accomplishment-item">
                  <div className="metric">{accomplishment.metric}</div>
                  <div className="metric-description">{accomplishment.description}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Project Portfolio from this role */}
      <section className="role-projects">
        <h2>Projects from this Role</h2>
        <div className="projects-grid">
          {job.projects.map((project, index) => (
            <div
              key={index}
              className="project-card"
            >
              <div className="project-image">
                <Suspense fallback={<div style={{minHeight: 200}}>Loading images...</div>}>
                  <ProjectImageSwiper
                    images={project.images}
                    title={project.title}
                    isNDA={isNDAProject(project)}
                  />
                </Suspense>
              </div>
              <div
                className="project-info"
                onClick={(e) => handleProjectInfoClick(e, project)}
                style={{ cursor: 'pointer' }}
              >
                <h3>{project.title}</h3>
                <p>{project.description}</p>
                <div className="project-tech">
                  {project.technologies.map((tech, techIndex) => (
                    <span key={techIndex}>{tech}</span>
                  ))}
                </div>
                {project.liveUrl && (
                  <div className="project-link">
                    <span>
                      {isNDAProject(project) ? 'Click to view NDA info →' : 'Click to view project →'}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* NDA Notification Modal */}
      <Suspense fallback={null}>
        <NDANotification
          isOpen={ndaNotification.isOpen}
          onClose={closeNdaNotification}
          projectTitle={ndaNotification.projectTitle}
        />
      </Suspense>

      <Footer />
    </div>
  );
};

export default JobDetail;
